Write a Python script to train a machine learning model that solves the TASK outlined below, 
using the approach outlined in the plan below. This must be a production-ready script.

# TASK:
{{ problem_statement }}

# PLAN:
{{ plan }}

# PREVIOUS ATTEMPTS, IF ANY:
{{history}}

# INSTRUCTIONS
Only return the code to train the model, no explanations outside the code. Any explanation should
be in the comments in the code itself, but your overall answer must only consist of the code script.

The script must assume that the data to be used for training is in the following files
relative to the current directory: {{ training_data_files }}

{% if use_validation_files %}
- The script must assume that the data to be used for validation is in the following files
relative to the current directory: {{ validation_data_files }}
{% endif %}

- The script must train the model, compute and print the final evaluation metric to standard output,
and **save all model files directly in the CURRENT directory** with descriptive names.
Do not create any subdirectories. Do not print ANY other text to standard output than the metric. Print the
metric in the format `metric_name: metric_value`.

- Use only {{ allowed_packages }}. Do NOT use any packages that are not part of this list of the Python standard library.

- Do not skip steps or combine preprocessors and models in the same joblib file.
- Do not simplify by ignoring important features; this is not an exploratory task, this script trains the model that
  will be used in production. For example, do not neglect to one-hot encode categorical features.

{% if use_validation_files %}
IMPORTANT: You MUST use the training datasets for training the model and the validation datasets for evaluating
the model performance. DO NOT mix these datasets during training.
    1. Load training data from the training_data_files
    2. Load validation data from the validation_data_files
    3. Train your model using ONLY the training data
    4. Evaluate your model using ONLY the validation data
    5. Compute the performance on the validation data
{% endif %}
