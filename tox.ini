[tox]
isolated_build = True
envlist = py311-light, py311-all, py312-light, py312-all

[testenv]
skip_install = true
allowlist_externals = poetry
passenv = *
commands =
    poetry run pytest {posargs}

# Run: tox -e py311-light -- tests/unit
[testenv:py311-light]
basepython = python3.11
commands_pre =
    poetry env use {envpython}
    poetry install

# Run: tox -e py311-all -- tests/unit
[testenv:py311-all]
basepython = python3.11
commands_pre =
    poetry env use {envpython}
    poetry install -E all

# Run: tox -e py312-light -- tests/unit
[testenv:py312-light]
basepython = python3.12
commands_pre =
    poetry env use {envpython}
    poetry install

# Run: tox -e py312-all -- tests/unit
[testenv:py312-all]
basepython = python3.12
commands_pre =
    poetry env use {envpython}
    poetry install -E all