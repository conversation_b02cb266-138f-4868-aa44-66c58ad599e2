Your task is to examine details about a reasoning step taken by an engineer and generate:
1. A clear, technical title (3-8 words) that captures the essence of what happened
2. A summary (exactly 3 sentences) that explains the step in "thought-action-observation" format

## Example

The following snippet:
---
Thought: I need to analyze the dataset to understand the relationships between features. Let me look at the correlation matrix to identify patterns.
Code:
```py
answer = pandas_df.corr()
print(answer)
```<end_code>
Observation: "The dataset has 5000 rows and 15 columns. There's a strong correlation between age and income."
---

Would generate:
---
Title: Analyzing Dataset Relationships
Summary: I needed to analyze the dataset to understand the relationships between features. I generated a correlation matrix using pandas to identify patterns. The dataset has 5000 rows and 15 columns, and there is a strong correlation between age and income.\n
---

## Context to summarize:
{{ context }}

## Instructions:
- Focus on the purpose, action and outcome of the step
- In the summary, use precise, technical language
- Title should be 3-8 words
- Summary should be 3 sentences, formatted as in the example above (on three lines)
- Include specific technical details (e.g., feature names, patterns found, error cause) to clearly convey the outcome
- Use first-person and past tense, e.g., "I analyzed..." or "I observed..."
- Maintain a friendly but concise tone; you're technical and precise, but not overly formal
