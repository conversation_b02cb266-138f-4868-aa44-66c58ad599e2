<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plexe Assistant</title>
    
    <!-- React and ReactDOM -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    
    <!-- Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
                'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
                sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background-color: #f9fafb;
        }
        
        #root {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .plexe-header {
            background-color: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }
        
        .plexe-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 48rem;
            margin: 0 auto;
            width: 100%;
            padding: 2rem 1rem;
        }
        
        /* Custom styles for assistant-ui components */
        .aui-root {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .aui-thread-root {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .aui-thread-viewport {
            flex: 1;
            overflow-y: auto;
            padding: 1.5rem;
        }
        
        .aui-composer-root {
            border-top: 1px solid #e5e7eb;
            padding: 1rem;
            background: #f9fafb;
        }
        
        .aui-composer-input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            font-size: 1rem;
            resize: none;
            outline: none;
            transition: border-color 0.15s;
        }
        
        .aui-composer-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .aui-message-root {
            margin-bottom: 1.5rem;
        }
        
        .aui-message-root[data-role="assistant"] {
            background: #f3f4f6;
            padding: 1rem;
            border-radius: 0.375rem;
        }
        
        .aui-message-content {
            line-height: 1.6;
        }
        
        .connection-status {
            position: absolute;
            top: 1rem;
            right: 2rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            color: #6b7280;
        }
        
        .status-indicator {
            width: 0.5rem;
            height: 0.5rem;
            border-radius: 50%;
            background-color: #10b981;
        }
        
        .status-indicator.disconnected {
            background-color: #ef4444;
        }
        
        @keyframes bounce {
            0%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
        }
        
        .animate-bounce {
            animation: bounce 1.4s infinite ease-in-out;
        }
    </style>
</head>
<body>
    <div id="root"></div>
    
    <script type="text/javascript">
        // Simple chat UI without assistant-ui dependency
        const { useState, useEffect, useRef } = React;
        const { createRoot } = ReactDOM;
        
        // WebSocket connection manager
        class WebSocketRuntime {
            constructor(url) {
                this.url = url;
                this.ws = null;
                this.subscribers = new Set();
                this.connectionStatus = 'disconnected';
                this.messageQueue = [];
                this.reconnectAttempts = 0;
                this.maxReconnectAttempts = 5;
                this.reconnectDelay = 1000;
            }
            
            connect() {
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    return;
                }
                
                this.ws = new WebSocket(this.url);
                
                this.ws.onopen = () => {
                    console.log('WebSocket connected');
                    this.connectionStatus = 'connected';
                    this.reconnectAttempts = 0;
                    this.notifyStatusChange();
                    
                    // Send any queued messages
                    while (this.messageQueue.length > 0) {
                        const message = this.messageQueue.shift();
                        this.ws.send(message);
                    }
                };
                
                this.ws.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        this.notifySubscribers(data);
                    } catch (error) {
                        console.error('Failed to parse WebSocket message:', error);
                    }
                };
                
                this.ws.onclose = () => {
                    console.log('WebSocket disconnected');
                    this.connectionStatus = 'disconnected';
                    this.notifyStatusChange();
                    this.attemptReconnect();
                };
                
                this.ws.onerror = (error) => {
                    console.error('WebSocket error:', error);
                };
            }
            
            attemptReconnect() {
                if (this.reconnectAttempts >= this.maxReconnectAttempts) {
                    console.error('Max reconnection attempts reached');
                    return;
                }
                
                this.reconnectAttempts++;
                const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
                
                console.log(`Attempting to reconnect in ${delay}ms...`);
                setTimeout(() => this.connect(), delay);
            }
            
            subscribe(callback) {
                this.subscribers.add(callback);
                return () => this.subscribers.delete(callback);
            }
            
            notifySubscribers(message) {
                this.subscribers.forEach(callback => callback(message));
            }
            
            notifyStatusChange() {
                this.subscribers.forEach(callback => 
                    callback({ type: 'status', status: this.connectionStatus })
                );
            }
            
            send(message) {
                const data = JSON.stringify({ content: message });
                
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    this.ws.send(data);
                } else {
                    // Queue message if not connected
                    this.messageQueue.push(data);
                    this.connect();
                }
            }
            
            disconnect() {
                if (this.ws) {
                    this.ws.close();
                    this.ws = null;
                }
            }
        }
        
        // Simple message component
        function Message({ message }) {
            const isUser = message.role === 'user';
            return React.createElement('div', {
                className: `flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`
            },
                React.createElement('div', {
                    className: `max-w-[70%] rounded-lg px-4 py-2 ${isUser ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-900'}`
                },
                    React.createElement('p', { className: 'whitespace-pre-wrap' }, message.content)
                )
            );
        }
        
        // Chat component
        function Chat({ wsRuntime }) {
            const [messages, setMessages] = useState([]);
            const [input, setInput] = useState('');
            const [isLoading, setIsLoading] = useState(false);
            const [userScrolledUp, setUserScrolledUp] = useState(false);
            const [showScrollButton, setShowScrollButton] = useState(false);
            const messagesEndRef = useRef(null);
            const messagesContainerRef = useRef(null);
            
            const scrollToBottom = () => {
                messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
                setShowScrollButton(false);
            };
            
            const handleScroll = (e) => {
                const { scrollTop, scrollHeight, clientHeight } = e.target;
                const isAtBottom = scrollHeight - scrollTop <= clientHeight + 10;
                setUserScrolledUp(!isAtBottom);
            };
            
            useEffect(() => {
                if (messages.length > 0) {
                    const lastMessage = messages[messages.length - 1];
                    if (lastMessage.role === 'user') {
                        // User sent message - always scroll to bottom
                        scrollToBottom();
                        setShowScrollButton(false);
                    } else {
                        // AI sent message - check if user scrolled up
                        if (userScrolledUp) {
                            setShowScrollButton(true);
                        } else {
                            scrollToBottom();
                        }
                    }
                }
            }, [messages, userScrolledUp]);
            
            useEffect(() => {
                const unsubscribe = wsRuntime.subscribe((data) => {
                    if (data.type === 'status') return;
                    setMessages(prev => [...prev, {
                        id: data.id || Date.now().toString(),
                        role: data.role || 'assistant',
                        content: data.content
                    }]);
                    setIsLoading(false);
                });
                return unsubscribe;
            }, [wsRuntime]);
            
            const handleSubmit = (e) => {
                e.preventDefault();
                if (!input.trim() || isLoading) return;
                const userMessage = {
                    id: Date.now().toString(),
                    role: 'user',
                    content: input.trim()
                };
                setMessages(prev => [...prev, userMessage]);
                setInput('');
                setIsLoading(true);
                wsRuntime.send(userMessage.content);
            };
            
            return React.createElement('div', { className: 'flex flex-col h-full' },
                // Messages area
                React.createElement('div', {
                    className: 'flex-1 overflow-y-auto p-4 relative',
                    onScroll: handleScroll,
                    ref: messagesContainerRef
                },
                    messages.length === 0 && React.createElement('div', { className: 'text-center text-gray-500 mt-8' },
                        React.createElement('p', { className: 'text-lg mb-2' }, '👋 Hello! I\'m your Plexe Assistant.'),
                        React.createElement('p', { className: 'text-sm' }, 'I help you build machine learning models through natural conversation.'),
                        React.createElement('p', { className: 'text-sm mt-2' }, 'Tell me about your ML problem, and I\'ll guide you through the process.')
                    ),
                    messages.map(msg => React.createElement(Message, { key: msg.id, message: msg })),
                    isLoading && React.createElement('div', { className: 'flex justify-start mb-4' },
                        React.createElement('div', { className: 'bg-gray-100 rounded-lg px-4 py-2' },
                            React.createElement('div', { className: 'flex space-x-2' },
                                React.createElement('div', { className: 'w-2 h-2 bg-gray-400 rounded-full animate-bounce' }),
                                React.createElement('div', { className: 'w-2 h-2 bg-gray-400 rounded-full animate-bounce', style: { animationDelay: '0.1s' } }),
                                React.createElement('div', { className: 'w-2 h-2 bg-gray-400 rounded-full animate-bounce', style: { animationDelay: '0.2s' } })
                            )
                        )
                    ),
                    React.createElement('div', { ref: messagesEndRef })
                ),
                // Scroll to bottom button
                showScrollButton && React.createElement('button', {
                    onClick: scrollToBottom,
                    className: 'fixed bottom-20 right-4 bg-blue-500 text-white rounded-full p-3 shadow-lg hover:bg-blue-600 transition-colors z-10'
                }, '↓'),
                // Input area
                React.createElement('form', { onSubmit: handleSubmit, className: 'border-t p-4' },
                    React.createElement('div', { className: 'flex space-x-2' },
                        React.createElement('input', {
                            type: 'text',
                            value: input,
                            onChange: (e) => setInput(e.target.value),
                            placeholder: 'Type your message...',
                            className: 'flex-1 px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500',
                            disabled: isLoading
                        }),
                        React.createElement('button', {
                            type: 'submit',
                            disabled: !input.trim() || isLoading,
                            className: 'px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed'
                        }, 'Send')
                    )
                )
            );
        }
        
        // Connection status component
        function ConnectionStatus({ runtime }) {
            const [status, setStatus] = useState('disconnected');
            
            useEffect(() => {
                const unsubscribe = runtime.subscribe((data) => {
                    if (data.type === 'status') {
                        setStatus(data.status);
                    }
                });
                
                return unsubscribe;
            }, [runtime]);
            
            return React.createElement('div', { className: 'connection-status' },
                React.createElement('div', { 
                    className: `status-indicator ${status === 'connected' ? '' : 'disconnected'}`
                }),
                React.createElement('span', null, 
                    status === 'connected' ? 'Connected' : 'Disconnected'
                )
            );
        }
        
        // Main App component
        function App() {
            const [wsRuntime, setWsRuntime] = useState(null);
            const [isConnected, setIsConnected] = useState(false);
            
            useEffect(() => {
                // Create WebSocket runtime
                const runtime = new WebSocketRuntime('ws://localhost:8000/ws');
                setWsRuntime(runtime);
                
                // Subscribe to connection status
                const unsubscribe = runtime.subscribe((data) => {
                    if (data.type === 'status') {
                        setIsConnected(data.status === 'connected');
                    }
                });
                
                // Connect to WebSocket
                runtime.connect();
                
                // Cleanup on unmount
                return () => {
                    unsubscribe();
                    runtime.disconnect();
                };
            }, []);
            
            if (!wsRuntime) {
                return React.createElement('div', { className: 'flex items-center justify-center h-screen' },
                    React.createElement('div', { className: 'text-center text-gray-500' },
                        'Initializing Plexe Assistant...'
                    )
                );
            }
            
            return React.createElement('div', { className: 'h-screen flex flex-col' },
                React.createElement('div', { className: 'plexe-header relative' },
                    React.createElement('h1', { className: 'text-2xl font-bold text-gray-900' },
                        'Plexe Assistant'
                    ),
                    React.createElement('p', { className: 'text-sm text-gray-600 mt-1' },
                        'Build ML models through natural conversation'
                    ),
                    React.createElement('div', { className: 'connection-status' },
                        React.createElement('div', { 
                            className: `status-indicator ${isConnected ? '' : 'disconnected'}`
                        }),
                        React.createElement('span', null, 
                            isConnected ? 'Connected' : 'Disconnected'
                        )
                    )
                ),
                React.createElement('div', { className: 'flex-1 plexe-container' },
                    React.createElement(Chat, { wsRuntime })
                )
            );
        }
        
        // Render the app
        const container = document.getElementById('root');
        const root = createRoot(container);
        root.render(React.createElement(App));
    </script>
</body>
</html>