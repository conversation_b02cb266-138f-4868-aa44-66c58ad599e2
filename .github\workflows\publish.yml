name: Publish to PyPI

on:
  release:
    types: [published]
  workflow_dispatch:

jobs:
  publish:
    runs-on: ubuntu-latest
    environment: release
    permissions:
      contents: read
    
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.12'

      - name: Install Poetry
        run: |
          python -m pip install --upgrade pip
          pip install poetry

      - name: Build Package
        run: poetry build

      - name: Configure PyPI Token
        run: poetry config pypi-token.pypi ${{ secrets.PYPI_TOKEN }}

      - name: Publish to PyPI
        if: github.event_name == 'release'
        run: poetry publish

      - name: List Build Output (for testing)
        if: github.event_name == 'workflow_dispatch'
        run: ls -l dist/