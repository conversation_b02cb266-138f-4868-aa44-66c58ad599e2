{"cells": [{"cell_type": "markdown", "id": "566c61d6a0c2cdb6", "metadata": {}, "source": ["# Data Generation Report\n", "This notebook contains an exploratory data analysis (EDA) report for the synthetic dataset {{$synthetic_data_path}}."]}, {"cell_type": "code", "execution_count": null, "id": "7001a63d4d4c77a4", "metadata": {}, "outputs": [], "source": ["import os\n", "import warnings\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "import seaborn as sns"]}, {"cell_type": "code", "execution_count": null, "id": "c00ab39ca74e5eb8", "metadata": {}, "outputs": [], "source": ["# configure the notebook\n", "warnings.filterwarnings(\"ignore\", module=\"seaborn*\")\n", "warnings.filterwarnings(\"ignore\", category=DeprecationWarning)\n", "\n", "sns.set_theme(rc={'figure.figsize': (25, 8)})\n", "\n", "# print for debugging purposes\n", "os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "5d44e8f2bb5f2e3f", "metadata": {}, "outputs": [], "source": ["# path placeholders replaced by actual paths during notebook generation\n", "SYNTHETIC_DATA_PATH = \"{{$synthetic_data_path}}\"\n", "REAL_DATA_PATH = \"{{$real_data_path}}\""]}, {"cell_type": "code", "execution_count": null, "id": "c404ba5d2a6735a3", "metadata": {}, "outputs": [], "source": ["# load the datasets\n", "df_synth = pd.read_csv(SYNTHETIC_DATA_PATH)\n", "\n", "df_real = None\n", "\n", "try:\n", "    df_real = pd.read_csv(REAL_DATA_PATH)\n", "    # only keep columns that are also in df_synth\n", "    df_real = df_real[df_real.columns.intersection(df_synth.columns)]\n", "except FileNotFoundError:\n", "    pass\n", "except ValueError:\n", "    pass"]}, {"cell_type": "markdown", "id": "ab499105539c444c", "metadata": {}, "source": "## Dataset Description\n"}, {"cell_type": "markdown", "id": "57266f57e9f06625", "metadata": {}, "source": "### Dataset Information"}, {"cell_type": "code", "execution_count": null, "id": "7042af0cea455a69", "metadata": {}, "outputs": [], "source": ["df_synth.info()"]}, {"cell_type": "code", "execution_count": null, "id": "5ed183e13d6a9c8b", "metadata": {}, "outputs": [], "source": ["if df_real is not None:\n", "    df_real.info()"]}, {"cell_type": "markdown", "id": "e3e1271a61500c44", "metadata": {}, "source": "### Dataset Statistics"}, {"cell_type": "code", "execution_count": null, "id": "c2c88e8361016bba", "metadata": {}, "outputs": [], "source": ["# synthetic data statistics\n", "df_synth.describe()"]}, {"cell_type": "code", "execution_count": null, "id": "48286b4bdf8ff695", "metadata": {}, "outputs": [], "source": ["# real data statistics, if available\n", "if df_real is not None:\n", "    df_real.describe()"]}, {"cell_type": "markdown", "id": "a1594a122f1255cc", "metadata": {}, "source": "### Dataset Examples"}, {"cell_type": "code", "execution_count": null, "id": "2a2011d54b988f4", "metadata": {}, "outputs": [], "source": ["# synthetic data examples\n", "pd.concat([df_synth.head(5), df_synth.tail(5)], axis=0)"]}, {"cell_type": "code", "execution_count": null, "id": "3f7550c2dbbeed9f", "metadata": {}, "outputs": [], "source": ["# real data examples, if available\n", "if df_real is not None:\n", "    pd.concat([df_real.head(5), df_real.tail(5)], axis=0)"]}, {"cell_type": "markdown", "id": "c2cc8891d1cc324d", "metadata": {}, "source": "## Data Quality"}, {"cell_type": "code", "execution_count": null, "id": "c9fdcabecd400601", "metadata": {}, "outputs": [], "source": ["df_synth.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "8df777a44c759365", "metadata": {}, "outputs": [], "source": ["# drop any column with a name that starts with Unnamed as it is likely an index\n", "df_synth = df_synth.loc[:, ~df_synth.columns.str.contains('^Unnamed')]\n", "\n", "if df_real is not None:\n", "    df_real = df_real.loc[:, ~df_real.columns.str.contains('^Unnamed')]"]}, {"cell_type": "markdown", "id": "3ee7a83fd1bcc3c8", "metadata": {}, "source": "## Data Visualisations"}, {"cell_type": "markdown", "id": "d85b663179a1b1ea", "metadata": {}, "source": "### Distributions (Individual Variables)"}, {"cell_type": "code", "execution_count": null, "id": "410c22c6ab53ae09", "metadata": {}, "outputs": [], "source": ["for column in df_synth.select_dtypes(include=[np.number]).columns:\n", "    plt.figure()\n", "\n", "    # add density plots, reference data only included if available\n", "    sns.histplot(df_synth[column], color='red', label='Synthetic', kde=True, stat=\"density\", linewidth=0)\n", "    if df_real is not None:\n", "        sns.histplot(df_real[column], color='blue', label='Reference', kde=True, stat=\"density\", linewidth=0)\n", "\n", "    # plot formatting\n", "    plt.title(f'Distribution of {column}')\n", "    plt.xlabel(column)\n", "    plt.ylabel('Density')\n", "    plt.legend()\n", "    plt.grid(True)\n", "    plt.show()"]}, {"cell_type": "markdown", "id": "3e5c51fce2f87739", "metadata": {}, "source": "### Correlations"}, {"cell_type": "code", "execution_count": null, "id": "f50412538692074c", "metadata": {}, "outputs": [], "source": ["# Correlation heatmaps for both datasets\n", "plt.figure()\n", "\n", "\n", "# for correlations, we need to map string variables to integer indices\n", "def indexing_map(x):\n", "    return x.map({val: idx for idx, val in enumerate(x.unique())}) if x.dtype == 'O' else x\n", "\n", "\n", "plt.subplot(1, 2, 1)\n", "sns.heatmap(df_synth.apply(indexing_map).corr(), annot=True, cmap='coolwarm', center=0)\n", "plt.title('Synthetic Data Correlation Matrix')\n", "\n", "if df_real is not None:\n", "    plt.subplot(1, 2, 2)\n", "    sns.heatmap(df_real.apply(indexing_map).corr(), annot=True, cmap='coolwarm', center=0)\n", "    plt.title('Reference Data Correlation Matrix')\n", "\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "55022633aa724623", "metadata": {}, "source": "### Principal Component Analysis"}, {"cell_type": "code", "execution_count": null, "id": "ca0d917e15652de5", "metadata": {}, "outputs": [], "source": ["# project the data into 2D space using PCA\n", "from sklearn.decomposition import PCA\n", "\n", "plt.figure()\n", "\n", "# project the synthetic data and plot it\n", "pca_synth = PCA(n_components=2)\n", "pca_synth.fit(df_synth.apply(indexing_map))\n", "\n", "plt.subplot(1, 2, 1)\n", "plt.scatter(pca_synth.transform(df_synth.apply(indexing_map))[:, 0],\n", "            pca_synth.transform(df_synth.apply(indexing_map))[:, 1])\n", "plt.title('Synthetic Data PCA')\n", "\n", "# project the real data, if available, and plot it\n", "# note that we use the same PCA model to ensure the same projection so that the data can be compared\n", "if df_real is not None:\n", "    plt.subplot(1, 2, 2)\n", "    plt.scatter(pca_synth.transform(df_real.apply(indexing_map))[:, 0],\n", "                pca_synth.transform(df_real.apply(indexing_map))[:, 1])\n", "    plt.title('Reference Data PCA')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "4b7a5a8f038679b7", "metadata": {}, "source": "## Similarity to Reference Data"}, {"cell_type": "markdown", "id": "19208b4657f1331", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>\n", "We perform the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> test on each individual continuous variable as a similarity measure for the individual\n", "variables. The output of the test is the p-value, which indicates the probability that the two samples are drawn from the\n", "same distribution. A low p-value indicates that the two samples are significantly different."]}, {"cell_type": "code", "execution_count": null, "id": "33c73869f69ae6bc", "metadata": {}, "outputs": [], "source": ["# perform the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> test on each continuous variable\n", "# for column in df_synth.select_dtypes(include=[np.floating]).columns:\n", "#     res = stats.kstest(df_synth[column], df_real[column])\n", "#     print(f'{column}: {res[\"pvalue\"]}')"]}, {"cell_type": "markdown", "id": "36a5c64364412f46", "metadata": {}, "source": ["### Chi-Square Test\n", "We perform the Chi-Square test on each individual categorical variable as a similarity measure for the individual\n", "variables. The output of the test is the p-value, which indicates the probability that the two samples are drawn from the\n", "same distribution. A low p-value indicates that the two samples are significantly different."]}, {"cell_type": "code", "execution_count": null, "id": "29140e5115b04e0f", "metadata": {}, "outputs": [], "source": ["# perform the Chi-Square test on each categorical variable\n", "# for column in df_synth.select_dtypes(exclude=[np.floating]).columns:\n", "#     res = stats.chisquare(df_synth[column].value_counts(), df_real[column].value_counts())\n", "#     print(f'{column}: {res.pvalue}')"]}, {"cell_type": "markdown", "id": "d75c8585b49ecfa0", "metadata": {}, "source": ["### Correlation Matrix Comparison\n", "We compare the correlation matrices of the synthetic and reference datasets using the Jensen-Shannon divergence.\n", "The Jensen-Shannon divergence is a measure of the similarity between two probability distributions.\n", "In this case, it is used to compare the correlation matrices of the synthetic and reference datasets, where the mutual\n", "information matrices are treated as probability distributions."]}, {"cell_type": "code", "execution_count": null, "id": "d29045501f0ad1ea", "metadata": {}, "outputs": [], "source": ["# # compute the correlation matrices\n", "# corr_synth = df_synth.apply(indexing_map).corr()\n", "# corr_real = df_real.apply(indexing_map).corr()\n", "# \n", "# # compute the norms of each individual matrix, and the difference between them\n", "# norm_synth = np.linalg.norm(corr_synth)\n", "# norm_real = np.linalg.norm(corr_real)\n", "# norm_diff = np.linalg.norm(corr_synth - corr_real)\n", "# \n", "# print(f\"synth: {norm_synth}, real: {norm_real}, diff: {norm_diff}\")"]}, {"cell_type": "markdown", "id": "c13dd2f82b29a94a", "metadata": {}, "source": ["### Variable-<PERSON>-<PERSON>\n", "We calculate the Jensen-Shannon distance between the distributions of each variable in the synthetic and reference datasets."]}, {"cell_type": "code", "execution_count": null, "id": "1aeb22213a0d7193", "metadata": {}, "outputs": [], "source": ["# # for each variable in the dataset, compute the Jensen-Shannon distance\n", "# distances = {}\n", "# \n", "# for column in df_synth.columns:\n", "#     if df_synth[column].dtype == np.number:\n", "#         column_pdf_synth = np.histogram(df_synth[column], bins=100, density=True)[0]\n", "#         column_pdf_real = np.histogram(df_real[column], bins=100, density=True)[0]\n", "#     else:\n", "#         column_pdf_synth = df_synth[column].value_counts(normalize=True)\n", "#         column_pdf_real = df_real[column].value_counts(normalize=True)\n", "#     \n", "#     # normalise the historigrams to make them valid probability distributions\n", "#     column_pdf_synth /= np.sum(column_pdf_synth)\n", "#     column_pdf_real /= np.sum(column_pdf_real)\n", "#     \n", "#     # compute the Jensen-Shannon distance\n", "#     jsd = distance.j<PERSON><PERSON><PERSON><PERSON>(column_pdf_synth, column_pdf_real)\n", "#     distances[column] = jsd\n", "    \n", "    "]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}