import os
import warnings

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns

# configure the notebook
warnings.filterwarnings("ignore", module="seaborn*")
warnings.filterwarnings("ignore", category=DeprecationWarning)

sns.set_theme(rc={'figure.figsize': (25, 8)})

# print for debugging purposes
os.getcwd()

# path placeholders replaced by actual paths during notebook generation
SYNTHETIC_DATA_PATH = "{{$synthetic_data_path}}"
REAL_DATA_PATH = "{{$real_data_path}}"

# load the datasets
df_synth = pd.read_csv(SYNTHETIC_DATA_PATH)

df_real = None

try:
    df_real = pd.read_csv(REAL_DATA_PATH)
    # only keep columns that are also in df_synth
    df_real = df_real[df_real.columns.intersection(df_synth.columns)]
except FileNotFoundError:
    pass
except ValueError:
    pass

df_synth.info()

if df_real is not None:
    df_real.info()

# synthetic data statistics
df_synth.describe()

# real data statistics, if available
if df_real is not None:
    df_real.describe()

# synthetic data examples
pd.concat([df_synth.head(5), df_synth.tail(5)], axis=0)

# real data examples, if available
if df_real is not None:
    pd.concat([df_real.head(5), df_real.tail(5)], axis=0)

df_synth.isnull().sum()

# drop any column with a name that starts with Unnamed as it is likely an index
df_synth = df_synth.loc[:, ~df_synth.columns.str.contains('^Unnamed')]

if df_real is not None:
    df_real = df_real.loc[:, ~df_real.columns.str.contains('^Unnamed')]

for column in df_synth.select_dtypes(include=[np.number]).columns:
    plt.figure()

    # add density plots, reference data only included if available
    sns.histplot(df_synth[column], color='red', label='Synthetic', kde=True, stat="density", linewidth=0)
    if df_real is not None:
        sns.histplot(df_real[column], color='blue', label='Reference', kde=True, stat="density", linewidth=0)

    # plot formatting
    plt.title(f'Distribution of {column}')
    plt.xlabel(column)
    plt.ylabel('Density')
    plt.legend()
    plt.grid(True)
    plt.show()

# Correlation heatmaps for both datasets
plt.figure()


# for correlations, we need to map string variables to integer indices
def indexing_map(x):
    return x.map({val: idx for idx, val in enumerate(x.unique())}) if x.dtype == 'O' else x


plt.subplot(1, 2, 1)
sns.heatmap(df_synth.apply(indexing_map).corr(), annot=True, cmap='coolwarm', center=0)
plt.title('Synthetic Data Correlation Matrix')

if df_real is not None:
    plt.subplot(1, 2, 2)
    sns.heatmap(df_real.apply(indexing_map).corr(), annot=True, cmap='coolwarm', center=0)
    plt.title('Reference Data Correlation Matrix')

plt.tight_layout()
plt.show()


# project the data into 2D space using PCA
from sklearn.decomposition import PCA

plt.figure()

# project the synthetic data and plot it
pca_synth = PCA(n_components=2)
pca_synth.fit(df_synth.apply(indexing_map))

plt.subplot(1, 2, 1)
plt.scatter(pca_synth.transform(df_synth.apply(indexing_map))[:, 0],
            pca_synth.transform(df_synth.apply(indexing_map))[:, 1])
plt.title('Synthetic Data PCA')

# project the real data, if available, and plot it
# note that we use the same PCA model to ensure the same projection so that the data can be compared
if df_real is not None:
    plt.subplot(1, 2, 2)
    plt.scatter(pca_synth.transform(df_real.apply(indexing_map))[:, 0],
                pca_synth.transform(df_real.apply(indexing_map))[:, 1])
    plt.title('Reference Data PCA')

plt.tight_layout()
plt.show()

# perform the Kolmogorov-Smirnov test on each continuous variable
# for column in df_synth.select_dtypes(include=[np.floating]).columns:
#     res = stats.kstest(df_synth[column], df_real[column])
#     print(f'{column}: {res["pvalue"]}')

# perform the Chi-Square test on each categorical variable
# for column in df_synth.select_dtypes(exclude=[np.floating]).columns:
#     res = stats.chisquare(df_synth[column].value_counts(), df_real[column].value_counts())
#     print(f'{column}: {res.pvalue}')

# # compute the correlation matrices
# corr_synth = df_synth.apply(indexing_map).corr()
# corr_real = df_real.apply(indexing_map).corr()
# 
# # compute the norms of each individual matrix, and the difference between them
# norm_synth = np.linalg.norm(corr_synth)
# norm_real = np.linalg.norm(corr_real)
# norm_diff = np.linalg.norm(corr_synth - corr_real)
# 
# print(f"synth: {norm_synth}, real: {norm_real}, diff: {norm_diff}")

# # for each variable in the dataset, compute the Jensen-Shannon distance
# distances = {}
# 
# for column in df_synth.columns:
#     if df_synth[column].dtype == np.number:
#         column_pdf_synth = np.histogram(df_synth[column], bins=100, density=True)[0]
#         column_pdf_real = np.histogram(df_real[column], bins=100, density=True)[0]
#     else:
#         column_pdf_synth = df_synth[column].value_counts(normalize=True)
#         column_pdf_real = df_real[column].value_counts(normalize=True)
#     
#     # normalise the historigrams to make them valid probability distributions
#     column_pdf_synth /= np.sum(column_pdf_synth)
#     column_pdf_real /= np.sum(column_pdf_real)
#     
#     # compute the Jensen-Shannon distance
#     jsd = distance.jensenshannon(column_pdf_synth, column_pdf_real)
#     distances[column] = jsd
    
    