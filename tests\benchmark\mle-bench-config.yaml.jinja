repo_url: https://github.com/openai/mle-bench.git
repo_dir: {{ repo_dir }}
provider: {{ provider }}
max_iterations: {{ max_iterations }}
timeout: {{ timeout }}
datasets:
{#  - 3d-object-detection-for-autonomous-vehicles#}
{#  - AI4Code#}
{#  - aerial-cactus-identification#}
{#  - alaska2-image-steganalysis#}
{#  - aptos2019-blindness-detection#}
{#  - billion-word-imputation#}
{#  - bms-molecular-translation#}
{#  - cassava-leaf-disease-classification#}
{#  - cdiscount-image-classification-challenge#}
{#  - chaii-hindi-and-tamil-question-answering#}
{#  - champs-scalar-coupling#}
{#  - denoising-dirty-documents#}
{#  - detecting-insults-in-social-commentary#}
{#  - dog-breed-identification#}
{#  - dogs-vs-cats-redux-kernels-edition#}
{#  - facebook-recruiting-iii-keyword-extraction#}
{#  - freesound-audio-tagging-2019#}
{#  - google-quest-challenge#}
{#  - google-research-identify-contrails-reduce-global-warming#}
{#  - h-and-m-personalized-fashion-recommendations#}
{#  - herbarium-2020-fgvc7#}
{#  - herbarium-2021-fgvc8#}
{#  - herbarium-2022-fgvc9#}
{#  - histopathologic-cancer-detection#}
{#  - hms-harmful-brain-activity-classification#}
{#  - hotel-id-2021-fgvc8#}
{#  - hubmap-kidney-segmentation#}
{#  - icecube-neutrinos-in-deep-ice#}
{#  - imet-2020-fgvc7#}
{#  - inaturalist-2019-fgvc6#}
{#  - invasive-species-monitoring#}
{#  - iwildcam-2019-fgvc6#}
{#  - iwildcam-2020-fgvc7#}
{#  - jigsaw-toxic-comment-classification-challenge#}
{#  - jigsaw-unintended-bias-in-toxicity-classification#}
{#  - kuzushiji-recognition#}
{#  - leaf-classification#}
{#  - learning-agency-lab-automated-essay-scoring-2#}
{#  - lmsys-chatbot-arena#}
{#  - ml2021spring-hw2#}
{#  - mlsp-2013-birds#}
{#  - movie-review-sentiment-analysis-kernels-only#}
{#  - multi-modal-gesture-recognition#}
{#  - new-york-city-taxi-fare-prediction#}
{#  - nfl-player-contact-detection#}
{#  - nomad2018-predict-transparent-conductors#}
{#  - osic-pulmonary-fibrosis-progression#}
{#  - paddy-disease-classification#}
{#  - petfinder-pawpularity-score#}
{#  - plant-pathology-2020-fgvc7#}
{#  - plant-pathology-2021-fgvc8#}
{#  - plant-seedlings-classification#}
{#  - playground-series-s3e18#}
{#  - predict-volcanic-eruptions-ingv-oe#}
{#  - random-acts-of-pizza#}
{#  - ranzcr-clip-catheter-line-classification#}
{#  - rsna-2022-cervical-spine-fracture-detection#}
{#  - rsna-breast-cancer-detection#}
{#  - rsna-miccai-brain-tumor-radiogenomic-classification#}
{#  - seti-breakthrough-listen#}
{#  - siim-covid19-detection#}
{#  - siim-isic-melanoma-classification#}
{#  - smartphone-decimeter-2022#}
  - spaceship-titanic
{#  - spooky-author-identification#}
{#  - stanford-covid-vaccine#}
{#  - statoil-iceberg-classifier-challenge#}
{#  - tabular-playground-series-dec-2021#}
  - tabular-playground-series-may-2022
{#  - tensorflow-speech-recognition-challenge#}
{#  - tensorflow2-question-answering#}
{#  - text-normalization-challenge-english-language#}
{#  - text-normalization-challenge-russian-language#}
{#  - tgs-salt-identification-challenge#}
{#  - the-icml-2013-whale-challenge-right-whale-redux#}
{#  - tweet-sentiment-extraction#}
{#  - us-patent-phrase-to-phrase-matching#}
{#  - uw-madison-gi-tract-image-segmentation#}
{#  - ventilator-pressure-prediction#}
{#  - vesuvius-challenge-ink-detection#}
{#  - vinbigdata-chest-xray-abnormalities-detection#}
{#  - whale-categorization-playground#}