---
name: Bug report
about: Create a report to help us improve
title: ''
labels: ''
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Define a model as '...'
2. Run the example on '....'
3. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Environment (please complete the following information):**
 - OS: [e.g. Windows 11]
 - Python Version [e.g. 3.12.0]
 - Other Versions, if relevant [e.g. Docker version, `pip` version]

**Additional context**
Add any other context about the problem here.
