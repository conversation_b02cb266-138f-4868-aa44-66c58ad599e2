[tool.poetry]
name = "plexe"
version = "0.26.2"
description = "An agentic framework for building ML models from natural language"
authors = [
    "<PERSON><PERSON> <mde<PERSON><PERSON><PERSON>@plexe.ai>",
    "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>"
]
license = "Apache-2.0"
readme = "README.md"
repository = "https://github.com/plexe-ai/plexe"
homepage = "https://github.com/plexe-ai/plexe"
packages = [{ include = "plexe" }]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: Apache Software License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules"
]
keywords = ["agent", "custom ai", "llm", "machine learning", "data generation"]

[tool.poetry.scripts]
plexe = "plexe.main:main"

[tool.poetry.dependencies]
python = ">=3.11,<3.13"
pandas = ">=1.5.0, <=2.2.0"
imbalanced-learn = "^0.12.4"
pydantic = "^2.9.2"
scikit-learn = ">=1.6.1,<1.7.0"
seaborn = "^0.12.2"
dataclasses-json = "^0.6.7"
bandit = "^1.8.2"
joblib = "^1.4.2"
mlxtend = "^0.23.4"
xgboost = "^2.1.3"
tenacity = "^9.0.0"
pyarrow = "^19.0.0"
litellm = "1.65.8"
statsmodels = "^0.14.4"
hypothesis = "^6.125.1"
numpy = ">=1.23.2,<2.0.0"
black = "^24.10.0"
jinja2 = "^3.1.6"
platformdirs = "^4.3.7"
mlflow = "^3.0.0rc2"
ray = "^2.9.0"
rich = "^13.7.1"
torch = "^2.3.0"
smolagents = "^1.14.0"
deprecated = "^1.2.18"

# Deep learning dependencies
transformers = { version = "^4.50.0", optional = true }
tokenizers = { version = "^0.21.1", optional = true }
accelerate = { version = "0.24.1", optional = true }
safetensors = { version = "^0.4.1", optional = true }

# Local web serving dependencies for chat UI
fastapi = { version = "^0.115.12", optional = true }
uvicorn = {extras = ["standard"], version = "^0.34.2", optional = true }
websockets = { version = "^12.0", optional = true }

[tool.poetry.extras]
transformers = ["transformers", "tokenizers", "accelerate", "safetensors"]
chatui = ["fastapi", "uvicorn", "websockets"]
all = [
    "transformers",
    "tokenizers",
    "accelerate",
    "safetensors",
    "fastapi",
    "uvicorn",
    "websockets"
]

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.4"
pre-commit = "^4.0.1"
ruff = "^0.9.1"
jupyterlab = "^4.2.5"
tox = "^4.14.1"
kaggle = "1.6.17"
pytest-cov = "^6.0.0"

[tool.semantic_release]
version_variables = ["pyproject.toml:version"]
commit_parser = "angular"
build_command = "poetry build"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 120
target-version = ["py312"]
exclude = '''
/(
    \.git
  | \.venv
  | __pycache__
  | build
  | dist
)/
'''

[tool.ruff]
line-length = 120
target-version = "py312"

[tool.ruff.lint]
ignore = [
    "E203", # Whitespace before ':'
    "E501", # Line length
    "E402", # Imports not at top of file
]