name: Integration

on:
  workflow_dispatch: {}
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
    types:
      - opened
      - synchronize
      - reopened

jobs:
  linting:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.12'

      - name: Install Dependencies
        run: |
          python -m pip install --upgrade pip
          pip install poetry
          poetry install

      - name: Format Code with Black
        run: poetry run black --check .

      - name: Lint Code with Ruff
        run: poetry run ruff check .

  run-tests:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ['3.11', '3.12']
        dependencies: ['lightweight', 'all']
    env:
      GOOGLE_API_KEY: ${{ vars.GOOGLE_API_KEY }}
      OPENAI_API_KEY: ${{ vars.OPENAI_API_KEY }}
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install Dependencies
        run: |
          python -m pip install --upgrade pip
          pip install poetry
          if [ "${{ matrix.dependencies }}" == "all" ]; then
            poetry install -E all
          else
            poetry install
          fi

      - name: Run Tests
        run: poetry run pytest tests/unit/